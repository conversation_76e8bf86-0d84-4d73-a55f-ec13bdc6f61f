plugins {
    kotlin("jvm")
}

dependencies {
    implementation(kotlin("stdlib"))
    implementation(libs.bundles.ktlint)
    testImplementation(testLibs.bundles.test.core)
    testRuntimeOnly(testLibs.bundles.test.core.runtime)
    testImplementation(testLibs.bundles.test.ktlint)
    testRuntimeOnly(testLibs.slf4j.simple)
}

tasks.getByName<Test>("test") {
    useJUnitPlatform()
}

tasks.withType<io.gitlab.arturbosch.detekt.Detekt> {
    config.setFrom(file("$projectDir/detekt.yml"))
}

tasks.register<Sync>("copyToLibs") {
    dependsOn(tasks.jar)
    into(project.layout.projectDirectory.dir("libs"))
    from(project.layout.buildDirectory.dir("libs"))
    include("*.jar")
}
