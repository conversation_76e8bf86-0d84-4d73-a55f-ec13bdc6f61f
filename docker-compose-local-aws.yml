services:
  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME-localstack_main}"
    image: localstack/localstack:4.1.1
    ports:
      - "127.0.0.1:4566:4566"   # Edge
      - "127.0.0.1:4572:4572"   # S3
      - "127.0.0.1:4574:4574"   # SFN
      - "127.0.0.1:4576:4576"   # SQS
      - "127.0.0.1:4577:4577"   # STS
    environment:
      - SERVICES=sqs,ses,s3,sfn,dynamodb,sts,iam,lambda
      - DEBUG=1
      - LS_LOG=trace            # verbose logs (prints storage paths)
      - PERSISTENCE=1           # enable on-disk state at /var/lib/localstack/state
      - LAMBDA_EXECUTOR=${LAMBDA_EXECUTOR-}
      - LOCALSTACK_API_KEY=${LOCALSTACK_API_KEY-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - /var/lib/localstack     # anonymous volume (in-container only)
      - /var/run/docker.sock:/var/run/docker.sock
      - ./docker/localstack:/var/localstack
      - ./docker/localstack/localstackSetup.sh:/etc/localstack/init/ready.d/init-aws.sh
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/DONE_WITH_SETUP"]
      interval: 1s
      timeout: 1s
      retries: 20
      start_period: 5s
